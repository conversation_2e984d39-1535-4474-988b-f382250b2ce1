"""
Simple test cases for StockCache utility that match the actual implementation.
"""

import os
import shutil
import tempfile
from datetime import datetime
from unittest.mock import Mock, patch

import pandas as pd
import pytest


class TestStockCache:
    """Test cases for StockCache utility."""

    def setup_method(self):
        """Setup test environment before each test."""
        # Import here to avoid circular imports
        import sys

        sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))

        from src.utils.stock_cache import StockCache

        self.StockCache = StockCache

        # Create temporary cache directory for testing
        self.temp_cache_dir = tempfile.mkdtemp()

    def teardown_method(self):
        """Cleanup after each test."""
        if os.path.exists(self.temp_cache_dir):
            shutil.rmtree(self.temp_cache_dir, ignore_errors=True)

    def test_stock_cache_initialization(self):
        """Test StockCache initialization."""
        cache = self.StockCache(cache_dir=self.temp_cache_dir)
        assert cache is not None
        assert hasattr(cache, "get_historical_data")
        assert hasattr(cache, "cache_dir")
        assert hasattr(cache, "cache_expiry")
        assert cache.cache_dir.exists()

    @patch("yfinance.download")
    def test_get_historical_data_success(self, mock_download):
        """Test successful historical data retrieval."""
        # Setup mock yfinance data
        dates = pd.date_range(start="2023-01-01", periods=10, freq="D")
        mock_data = pd.DataFrame(
            {"Open": [150.0] * 10, "High": [155.0] * 10, "Low": [145.0] * 10, "Close": [152.0] * 10, "Volume": [1000000] * 10},
            index=dates,
        )
        mock_download.return_value = mock_data

        cache = self.StockCache(cache_dir=self.temp_cache_dir)
        data = cache.get_historical_data("AAPL")

        assert not data.empty
        assert len(data) == 10
        assert "Close" in data.columns
        mock_download.assert_called_once()

    @patch("yfinance.download")
    def test_get_historical_data_empty_response(self, mock_download):
        """Test handling of empty yfinance response."""
        mock_download.return_value = pd.DataFrame()

        cache = self.StockCache(cache_dir=self.temp_cache_dir)
        data = cache.get_historical_data("INVALID")

        assert data.empty
        mock_download.assert_called_once()

    @patch("yfinance.download")
    def test_get_historical_data_exception(self, mock_download):
        """Test handling of yfinance exceptions."""
        mock_download.side_effect = Exception("Network error")

        cache = self.StockCache(cache_dir=self.temp_cache_dir)
        data = cache.get_historical_data("AAPL")

        assert data.empty
        mock_download.assert_called_once()

    def test_cache_directory_creation(self):
        """Test that cache directory is created properly."""
        cache = self.StockCache(cache_dir=self.temp_cache_dir)
        assert cache.cache_dir.exists()
        assert cache.cache_dir.is_dir()

    def test_cache_expiry_setting(self):
        """Test cache expiry configuration."""
        # Test default expiry
        cache1 = self.StockCache(cache_dir=self.temp_cache_dir)
        assert cache1.cache_expiry == 24 * 60 * 60  # 24 hours

        # Test custom expiry
        cache2 = self.StockCache(cache_dir=self.temp_cache_dir, cache_expiry=3600)  # 1 hour
        assert cache2.cache_expiry == 3600

    def test_symbol_cleaning(self):
        """Test that symbols are properly cleaned."""
        cache = self.StockCache(cache_dir=self.temp_cache_dir)

        # Test with quotes and whitespace
        with patch("yfinance.download") as mock_download:
            mock_download.return_value = pd.DataFrame()
            cache.get_historical_data(' "AAPL" ')
            # Should call with cleaned symbol
            mock_download.assert_called_with("AAPL", period="1y", interval="1h", progress=False, auto_adjust=True)

    def test_cache_file_creation(self):
        """Test that cache files are created properly."""
        cache = self.StockCache(cache_dir=self.temp_cache_dir)

        with patch("yfinance.download") as mock_download:
            # Create test data
            dates = pd.date_range(start="2023-01-01", periods=5, freq="D")
            test_data = pd.DataFrame(
                {"Open": [150.0] * 5, "High": [155.0] * 5, "Low": [145.0] * 5, "Close": [152.0] * 5, "Volume": [1000000] * 5},
                index=dates,
            )
            mock_download.return_value = test_data

            # Get data (should create cache file)
            data = cache.get_historical_data("AAPL")

            # Check that cache file was created
            cache_file = cache.cache_dir / "AAPL_data.parquet"
            assert cache_file.exists()

            # Verify data integrity
            assert not data.empty
            assert len(data) == 5
